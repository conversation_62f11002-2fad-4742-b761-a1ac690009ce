"""
NSFW Image Detection Test Script
Based on the README.md file in model_path/nsfw_detection/

This script implements three different approaches for NSFW image classification:
1. Transformers Pipeline approach (high-level)
2. Direct model loading approach (low-level)
3. YOLO approach using ONNX runtime

Author: Generated by Augment Agent
"""

import os
import sys
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Import transformers components
try:
    from transformers import pipeline, AutoModelForImageClassification, ViTImageProcessor
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("Warning: transformers library not available. Install with: pip install transformers")
    TRANSFORMERS_AVAILABLE = False

# Import ONNX runtime for YOLO approach
try:
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    print("Warning: onnxruntime not available. Install with: pip install onnxruntime")
    ONNX_AVAILABLE = False


class NSFWDetector:
    """
    A comprehensive NSFW detection class that supports multiple model approaches.
    """

    def __init__(self, model_path="model_path/nsfw_detection"):
        """
        Initialize the NSFW detector.

        Args:
            model_path (str): Path to the model directory
        """
        self.model_path = Path(model_path)
        self.labels_path = self.model_path / "labels.json"

        # Load labels
        self.labels = self._load_labels()

        # Initialize models (lazy loading)
        self.pipeline_classifier = None
        self.direct_model = None
        self.direct_processor = None
        self.onnx_session = None

    def _load_labels(self):
        """Load class labels from JSON file."""
        try:
            with open(self.labels_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Warning: Labels file not found at {self.labels_path}")
            return {"0": "normal", "1": "nsfw"}  # Default labels

    def _load_image(self, image_path):
        """
        Load and validate image file.

        Args:
            image_path (str): Path to image file

        Returns:
            PIL.Image: Loaded image in RGB format
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")

        try:
            image = Image.open(image_path).convert("RGB")
            return image
        except Exception as e:
            raise ValueError(f"Error loading image {image_path}: {e}")

    def predict_with_pipeline(self, image_path):
        """
        Method 1: Use transformers pipeline for classification (high-level approach).

        Args:
            image_path (str): Path to image file

        Returns:
            dict: Prediction results with confidence scores
        """
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers library is required for pipeline approach")

        # Lazy load pipeline
        if self.pipeline_classifier is None:
            print("Loading pipeline classifier...")
            self.pipeline_classifier = pipeline(
                "image-classification",
                model=str(self.model_path)
            )

        # Load and classify image
        img = self._load_image(image_path)
        results = self.pipeline_classifier(img)

        return {
            'method': 'pipeline',
            'predictions': results,
            'top_prediction': results[0] if results else None
        }

    def predict_with_direct_model(self, image_path):
        """
        Method 2: Use direct model loading approach (low-level approach).

        Args:
            image_path (str): Path to image file

        Returns:
            dict: Prediction results with confidence scores
        """
        if not TRANSFORMERS_AVAILABLE:
            raise ImportError("transformers library is required for direct model approach")

        # Lazy load model and processor
        if self.direct_model is None or self.direct_processor is None:
            print("Loading direct model and processor...")
            self.direct_model = AutoModelForImageClassification.from_pretrained(str(self.model_path))
            self.direct_processor = ViTImageProcessor.from_pretrained(str(self.model_path))

        # Load and process image
        img = self._load_image(image_path)

        with torch.no_grad():
            inputs = self.direct_processor(images=img, return_tensors="pt")
            outputs = self.direct_model(**inputs)
            logits = outputs.logits

            # Get probabilities
            probabilities = torch.nn.functional.softmax(logits, dim=-1)
            predicted_label_idx = logits.argmax(-1).item()
            predicted_label = self.direct_model.config.id2label[predicted_label_idx]
            confidence = probabilities[0][predicted_label_idx].item()

        return {
            'method': 'direct_model',
            'predicted_label': predicted_label,
            'predicted_index': predicted_label_idx,
            'confidence': confidence,
            'all_probabilities': {
                self.direct_model.config.id2label[i]: prob.item()
                for i, prob in enumerate(probabilities[0])
            }
        }

    def predict_with_yolo(self, image_path, model_path=None, input_size=(224, 224)):
        """
        Method 3: Use YOLO approach with ONNX runtime.

        Args:
            image_path (str): Path to image file
            model_path (str): Path to ONNX model file (optional)
            input_size (tuple): Input size for the model (height, width)

        Returns:
            dict: Prediction results
        """
        if not ONNX_AVAILABLE:
            raise ImportError("onnxruntime is required for YOLO approach")

        # Use default YOLO model path if not provided
        if model_path is None:
            # Look for ONNX model in the model directory
            onnx_files = list(self.model_path.glob("*.onnx"))
            if not onnx_files:
                raise FileNotFoundError("No ONNX model found in model directory")
            model_path = str(onnx_files[0])

        # Lazy load ONNX session
        if self.onnx_session is None:
            print(f"Loading ONNX model from {model_path}...")
            self.onnx_session = ort.InferenceSession(model_path)

        # Load and preprocess image
        original_image = self._load_image(image_path)
        image_resized = original_image.resize(input_size, Image.Resampling.BILINEAR)
        image_np = np.array(image_resized, dtype=np.float32) / 255.0
        image_np = np.transpose(image_np, (2, 0, 1))  # [C, H, W]
        input_tensor = np.expand_dims(image_np, axis=0).astype(np.float32)

        # Run inference
        input_name = self.onnx_session.get_inputs()[0].name
        output_name = self.onnx_session.get_outputs()[0].name
        outputs = self.onnx_session.run([output_name], {input_name: input_tensor})
        predictions = outputs[0]

        # Postprocess predictions
        predicted_index = np.argmax(predictions)
        predicted_label = self.labels[str(predicted_index)]
        confidence = float(np.max(predictions))

        return {
            'method': 'yolo_onnx',
            'predicted_label': predicted_label,
            'predicted_index': int(predicted_index),
            'confidence': confidence,
            'raw_predictions': predictions.tolist()
        }

    def compare_all_methods(self, image_path):
        """
        Compare results from all available methods.

        Args:
            image_path (str): Path to image file

        Returns:
            dict: Results from all methods
        """
        results = {}

        # Method 1: Pipeline
        if TRANSFORMERS_AVAILABLE:
            try:
                results['pipeline'] = self.predict_with_pipeline(image_path)
                print("✓ Pipeline method completed")
            except Exception as e:
                results['pipeline'] = {'error': str(e)}
                print(f"✗ Pipeline method failed: {e}")

        # Method 2: Direct model
        if TRANSFORMERS_AVAILABLE:
            try:
                results['direct_model'] = self.predict_with_direct_model(image_path)
                print("✓ Direct model method completed")
            except Exception as e:
                results['direct_model'] = {'error': str(e)}
                print(f"✗ Direct model method failed: {e}")

        # Method 3: YOLO (only if ONNX model exists)
        if ONNX_AVAILABLE:
            try:
                results['yolo'] = self.predict_with_yolo(image_path)
                print("✓ YOLO method completed")
            except Exception as e:
                results['yolo'] = {'error': str(e)}
                print(f"✗ YOLO method failed: {e}")

        return results

    def display_results(self, image_path, results=None):
        """
        Display image with prediction results.

        Args:
            image_path (str): Path to image file
            results (dict): Prediction results (optional, will compute if not provided)
        """
        if results is None:
            results = self.compare_all_methods(image_path)

        # Load image for display
        img = self._load_image(image_path)

        # Create subplot for image and results
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # Display image
        ax1.imshow(img)
        ax1.set_title(f"Input Image: {os.path.basename(image_path)}", fontsize=12)
        ax1.axis('off')

        # Display results text
        ax2.axis('off')
        result_text = f"NSFW Detection Results\n{'='*30}\n\n"

        for method, result in results.items():
            if 'error' in result:
                result_text += f"{method.upper()}:\n  Error: {result['error']}\n\n"
            else:
                if method == 'pipeline' and 'top_prediction' in result:
                    pred = result['top_prediction']
                    result_text += f"{method.upper()}:\n  Label: {pred['label']}\n  Confidence: {pred['score']:.4f}\n\n"
                elif method in ['direct_model', 'yolo']:
                    result_text += f"{method.upper()}:\n  Label: {result['predicted_label']}\n  Confidence: {result['confidence']:.4f}\n\n"

        ax2.text(0.05, 0.95, result_text, transform=ax2.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()
        plt.show()


def test_single_image(image_path, model_path="model_path/nsfw_detection"):
    """
    Test NSFW detection on a single image using all available methods.

    Args:
        image_path (str): Path to the test image
        model_path (str): Path to the model directory
    """
    print(f"Testing NSFW detection on: {image_path}")
    print("="*50)

    # Initialize detector
    detector = NSFWDetector(model_path)

    # Run all methods and display results
    results = detector.compare_all_methods(image_path)
    detector.display_results(image_path, results)

    return results


def test_batch_images(image_dir, model_path="model_path/nsfw_detection", extensions=('.jpg', '.jpeg', '.png', '.bmp')):
    """
    Test NSFW detection on multiple images in a directory.

    Args:
        image_dir (str): Directory containing test images
        model_path (str): Path to the model directory
        extensions (tuple): Supported image file extensions
    """
    image_dir = Path(image_dir)
    if not image_dir.exists():
        print(f"Error: Directory {image_dir} does not exist")
        return

    # Find all image files
    image_files = []
    for ext in extensions:
        image_files.extend(image_dir.glob(f"*{ext}"))
        image_files.extend(image_dir.glob(f"*{ext.upper()}"))

    if not image_files:
        print(f"No image files found in {image_dir}")
        return

    print(f"Found {len(image_files)} image(s) in {image_dir}")
    print("="*50)

    # Initialize detector
    detector = NSFWDetector(model_path)

    # Process each image
    all_results = {}
    for i, image_path in enumerate(image_files, 1):
        print(f"\nProcessing image {i}/{len(image_files)}: {image_path.name}")
        try:
            results = detector.compare_all_methods(str(image_path))
            all_results[str(image_path)] = results

            # Display results for this image
            detector.display_results(str(image_path), results)

        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            all_results[str(image_path)] = {'error': str(e)}

    return all_results


def print_system_info():
    """Print system information and available libraries."""
    print("NSFW Detection System Information")
    print("="*40)
    print(f"Python version: {sys.version}")
    print(f"PyTorch available: {torch.__version__ if 'torch' in globals() else 'Not installed'}")
    print(f"Transformers available: {TRANSFORMERS_AVAILABLE}")
    print(f"ONNX Runtime available: {ONNX_AVAILABLE}")
    print(f"PIL/Pillow available: {'Yes' if 'Image' in globals() else 'No'}")
    print(f"Matplotlib available: {'Yes' if 'plt' in globals() else 'No'}")
    print()


def main():
    """Main function to demonstrate NSFW detection capabilities."""
    print_system_info()

    # Define paths
    model_path = "../model_path/nsfw_detection"
    test_images_dir = "./img"

    # Check if model directory exists
    if not os.path.exists(model_path):
        print(f"Error: Model directory not found at {model_path}")
        print("Please ensure the model files are downloaded and placed in the correct directory.")
        return

    # Test with available test images
    if os.path.exists(test_images_dir):
        print(f"Testing with images from: {test_images_dir}")
        test_batch_images(test_images_dir, model_path)
    else:
        print(f"Test images directory not found: {test_images_dir}")

        # Try to find any image files in the project
        project_root = Path(".")
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend(project_root.rglob(f"*{ext}"))

        if image_files:
            print(f"Found {len(image_files)} image file(s) in project:")
            for img in image_files[:5]:  # Show first 5
                print(f"  - {img}")

            # Test with the first image found
            test_image = str(image_files[0])
            print(f"\nTesting with: {test_image}")
            test_single_image(test_image, model_path)
        else:
            print("No test images found in the project.")
            print("\nTo test the detection:")
            print("1. Place test images in 'model_path/nsfw_mosaic/test_images/' directory")
            print("2. Or call test_single_image('path/to/your/image.jpg')")


if __name__ == "__main__":
    main()